# 代码Review规范

## Review检查维度

### 1. 基础代码规范检查
- **注释规范**: 检查@author和@date是否符合规范
- **实体类规范**: 确认使用@Setter/@Getter而非@Data
- **字符串处理**: 验证使用StringUtils.isEmpty进行字符串判空
- **禁用检查**: 确认未使用BeanUtils.copyProperties
- **文件大小**: 检查单文件是否超过1000行

### 2. 代码质量检查
- **命名规范**: 类名、方法名、变量名是否符合驼峰命名规则
- **代码结构**: 方法长度是否合理，逻辑是否清晰
- **异常处理**: 是否有适当的异常处理机制
- **资源管理**: 数据库连接、文件流等资源是否正确关闭
- **线程安全**: 多线程环境下的代码是否线程安全

### 3. 业务逻辑检查
- **边界条件**: 是否处理了边界情况和异常情况
- **数据验证**: 输入参数是否进行了充分验证
- **业务一致性**: 业务逻辑是否符合需求规范
- **性能考虑**: 是否存在明显的性能问题

### 4. 安全检查
- **SQL注入**: 是否存在SQL注入风险
- **XSS防护**: 前端交互是否有XSS防护
- **数据脱敏**: 敏感数据是否进行了适当处理
- **权限控制**: 是否有适当的权限验证

### 5. 架构规范检查
- **分层架构**: 是否符合Controller-Service-DAO分层结构
- **依赖注入**: 是否正确使用Spring的依赖注入
- **事务管理**: 数据库事务是否正确使用
- **配置管理**: 配置项是否合理管理

## Review严重级别

### 高危问题 (Critical)
- 安全漏洞
- 数据丢失风险
- 系统崩溃风险
- 违反核心编码规范

### 重要问题 (Major)
- 性能问题
- 业务逻辑错误
- 代码质量问题
- 违反一般编码规范

### 一般问题 (Minor)
- 代码风格问题
- 注释不完整
- 命名不规范
- 代码重复

### 建议改进 (Suggestion)
- 可读性改进
- 性能优化建议
- 架构优化建议

## Review输出格式

### 问题报告格式
```
### 问题类型: [高危/重要/一般/建议]
- **文件**: `{文件路径}:{行号}`
- **问题描述**: {具体问题说明}
- **修改建议**: {具体修改方案}
- **影响范围**: {该问题可能造成的影响}
```

### 总体评估格式
```
## 代码Review总结
- **Review范围**: {Review的代码范围}
- **总体评分**: {评分}/10
- **高危问题**: {数量}个
- **重要问题**: {数量}个
- **一般问题**: {数量}个
- **建议改进**: {数量}个
- **Review结论**: [通过/需修改/拒绝]
```