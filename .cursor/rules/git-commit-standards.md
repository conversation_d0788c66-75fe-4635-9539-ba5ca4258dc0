# Git提交规范

## 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

## 提交类型 (type)
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式修改(不影响代码逻辑)
- **refactor**: 重构代码(既不是新增功能也不是修复bug)
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动
- **revert**: 回滚之前的commit

## 作用域 (scope)
- **controller**: 控制层
- **service**: 服务层
- **dao**: 数据访问层
- **entity**: 实体类
- **config**: 配置相关
- **util**: 工具类
- **test**: 测试代码

## 提交信息示例
```
feat(service): 添加用户查询接口

- 实现根据用户ID查询用户信息功能
- 添加参数验证和异常处理
- 完善相关单元测试

Closes #123
```

## 代码Review检查点

### 提交粒度检查
- 每次提交应该是一个逻辑完整的功能点
- 避免将多个不相关的修改放在同一次提交中
- 提交信息应该清晰说明本次修改的内容

### 代码变更检查
- 删除的调试代码和注释
- 确认没有提交敏感信息(密码、密钥等)
- 检查是否有不必要的文件被提交
- 验证代码格式是否符合项目规范

### 影响范围评估
- 评估本次修改可能影响的功能模块
- 确认是否需要更新相关文档
- 检查是否影响现有的测试用例
- 评估是否需要数据库迁移脚本