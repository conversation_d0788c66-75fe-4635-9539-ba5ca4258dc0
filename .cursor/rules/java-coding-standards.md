# Java编码规范

## 基本规范

### 注释规范
- 类和方法注释中，不得更新或覆盖原有的@author和@date值
- 注释作者统一为：hongdong.xie
- 注释不作为代码review参照标准
- 通过执行命令获取当前机器时间用于@date时间赋值

### 实体类规范
- VO/PO/DTO/BO等实体类中，使用@Setter/@Getter注解
- 禁止使用@Data注解
- 禁止使用BeanUtils.copyProperties进行类属性复制

### 字符串处理
- 字符串判空统一使用org.apache.commons.lang3.StringUtils.isEmpty
- 不得使用其他字符串判空方式

### 文件大小控制
- 尽量保证单个Java文件不超过1000行
- 超过1000行的文件需要考虑拆分重构

## 代码质量要求
- 代码必须严格符合项目规范
- 遵循现有的代码风格和命名约定
- 保持代码的可读性和可维护性

## 文档创建规范
- 除非明确要求，否则不主动创建文档文件
- 优先编辑现有文件而非创建新文件
- 不主动创建README或说明文档

## 测试规范
- 除非明确要求，否则不生成单元测试
- 不提供优化版本或替代实现
- 严格按照用户要求交付功能

## 输出规范
- 交付确切的用户需求，不多不少
- 完成请求的代码/内容后立即停止输出
- 不产生总结文档或概述