
# AI提示词（第二步）：基于真实数据库数据生成接口测试用例 JSON 输入参数

## 🎯 任务目标
请根据我提供的接口测试用例文档内容，**补充每个测试用例中“输入参数 JSON”字段**，所使用的数据必须来自真实数据库中查询得到的有效数据。

## 🧭 执行流程
1. **文档解析**
   阅读测试用例文档内容（Markdown格式），提取每个用例下的“输入参数 JSON”结构，理解字段含义与数据依赖。
2. **数据表确认**
   使用文档中列出的“数据库相关表”作为数据源，构建查询逻辑。
3. **MySQL查询操作**
   利用 MCP 或 SQL 工具，从数据库中提取实际存在的数据，用以填充参数字段（如 deptId、客户编号、昵称等）。
4. **参数生成**
   为每个测试用例生成一个可用的、逻辑合理的输入 JSON 数据，保持原字段结构，替换空值。
5. **直接修改文档**
   **⚠️ 重要：必须使用文件修改工具直接修改测试用例文档，将生成的JSON数据填充到表格的"输入参数json"列中，不要仅输出JSON数据。**
6. **边界与异常处理**
   - 对于异常场景的测试用例，请构造“不存在的”或非法参数值（如 deptId 使用 `999999`）；
   - 正常用例应保持参数逻辑一致、上下游数据有效关联。
7. **测试数据准备**
   - 如发现数据库中缺乏满足测试场景所需的数据，应记录具体缺失情况；
   - 可以建议生成所需测试数据或人工补充；
   - 输出中请标注该用例的输入为“模拟数据”或“待准备数据”，以便测试人员后续处理；
   - 如果需要准备数据，请在输出中标注“待准备数据”，并给出准备数据的描述和对应sql脚本。

## 📦 输出格式要求
- **必须直接修改测试用例文档**：将生成的JSON数据填充到测试用例表格中的"输入参数json"列；
- **不要单独输出JSON**：避免仅输出JSON数据块而不更新原文档；
- **确保表格完整性**：每个测试用例的"输入参数json"字段都必须填写完整的JSON格式数据；
- **保持原有结构**：修改时保持测试用例文档的原有格式和结构不变；
- **JSON格式要求**：确保JSON语法正确，字段完整，可直接用于自动化测试。

## ✅ 示例操作
**错误做法（仅输出JSON）：**
````json
{"deptIdList": [650], "hbOneNo": "9200000103", "wechatNickName": "赵木", "pageNo": 1, "pageSize": 10}
````

**正确做法（直接修改文档）：**
使用文件修改工具修改测试用例文档，将JSON数据填入表格：
```
| **TC-001** | 查询测试 | ... | ... | ... | {"deptIdList": [650], "hbOneNo": "9200000103", "wechatNickName": "赵木", "pageNo": 1, "pageSize": 10} | ... |
```

## 🧩 数据质量要求
- **真实性**：参数值必须来源于数据库中真实存在的记录；
- **业务关联性**：参数间应具有关联逻辑（如 deptId 与 wechatNickName 属于同一组织结构）；
- **合法性与完整性**：JSON 字段完整、语法正确，可直接用于自动化测试；
- **测试覆盖性**：涵盖正常、异常、边界场景；
- **通用性**：避免使用敏感或唯一标识（如手机号等），选用可复用的泛化样例。

## ✅ 操作检查清单
**执行前必须确认：**
- [ ] 已使用SQL工具查询数据库获取真实数据
- [ ] 已识别测试用例文档中的"输入参数json"列
- [ ] 准备使用文件修改工具修改文档（不是输出JSON），确保数据是json格式

**执行后必须验证：**
- [ ] 每个测试用例的"输入参数json"列都已填写
- [ ] JSON格式正确且字段完整
- [ ] 正常用例使用真实数据，异常用例使用不存在的数据
- [ ] 文档结构和格式保持不变

## 🧾 附加说明
- 如果某字段值无法通过查询获取，请用 `"示例值"` 或合理伪造的样例值替代；
- 若同一测试用例中涉及多个字段取值来源，请确保它们之间存在实际业务数据关系；
- 所有字段必须填写完整，不能删除字段或结构。

## 🧠 适用场景
- 接口自动化测试数据生成
- 基于真实数据的模拟场景构建
- 接口用例维护与测试文档标准化
- 提高测试的准确性与执行可行性
